import 'dart:io';
import 'dart:typed_data';
import 'package:bloc/bloc.dart';
import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/core/utils/form_utils.dart';
import 'package:storetrack_app/core/utils/image_storage_utils.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/features/home/<USER>/mappers/task_detail_mapper.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

import 'signature_page_state.dart';

class SignaturePageCubit extends Cubit<SignaturePageState> {
  SignaturePageCubit() : super(SignaturePageInitial());

  /// Load form data and existing signature from database
  Future<void> loadFormData({
    required int taskId,
    required int formId,
  }) async {
    try {
      emit(SignaturePageLoading());

      // Load task and form data from database
      final taskWithForm = await FormUtils.getTaskWithFormModel(taskId, formId);

      if (taskWithForm == null) {
        emit(const SignaturePageError('Task or form not found in database'));
        return;
      }

      final taskEntity = taskWithForm['task'] as entities.TaskDetail;
      final formEntity =
          taskEntity.forms?.where((form) => form.formId == formId).firstOrNull;

      if (formEntity == null) {
        emit(const SignaturePageError('Form not found'));
        return;
      }

      // Load question answers for this form
      final questionAnswers = await _loadQuestionAnswers(taskId, formId);

      // Check for existing signature
      final existingSignatureUrl = await _getExistingSignature(taskId, formId);

      emit(SignaturePageLoaded(
        task: taskEntity,
        form: formEntity,
        questionAnswers: questionAnswers,
        existingSignatureUrl: existingSignatureUrl,
      ));
    } catch (e) {
      logger('SignaturePageCubit: Error loading form data: $e');
      emit(SignaturePageError('Error loading form data: ${e.toString()}'));
    }
  }

  /// Save signature to database and file storage
  Future<void> saveSignature({
    required int taskId,
    required int formId,
    required Uint8List signatureBytes,
    required String signedBy,
  }) async {
    final currentState = state;
    if (currentState is! SignaturePageLoaded) return;

    try {
      emit(SignaturePageSaving(
        task: currentState.task,
        form: currentState.form,
        questionAnswers: currentState.questionAnswers,
      ));

      // Save signature image to app storage
      final signatureUrl = await _saveSignatureImage(signatureBytes);
      if (signatureUrl == null) {
        emit(const SignaturePageError('Failed to save signature image'));
        return;
      }

      // Save signature to database
      await _saveSignatureToDatabase(
        taskId: taskId,
        formId: formId,
        signatureUrl: signatureUrl,
        signedBy: signedBy,
      );

      emit(SignaturePageSaved(signatureUrl));
    } catch (e) {
      logger('SignaturePageCubit: Error saving signature: $e');
      emit(SignaturePageError('Error saving signature: ${e.toString()}'));
    }
  }

  /// Load question answers for the form
  Future<List<entities.QuestionAnswer>> _loadQuestionAnswers(
      int taskId, int formId) async {
    try {
      final realm = RealmDatabase.instance.realm;
      final taskModel =
          realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;

      if (taskModel == null) return [];

      final formModel =
          taskModel.forms.where((form) => form.formId == formId).firstOrNull;

      if (formModel == null) return [];

      // Convert to entity and return question answers
      final taskEntity = TaskDetailMapper.toEntity(taskModel);
      final formEntity =
          taskEntity.forms?.where((form) => form.formId == formId).firstOrNull;

      return formEntity?.questionAnswers ?? [];
    } catch (e) {
      logger('Error loading question answers: $e');
      return [];
    }
  }

  /// Check for existing signature
  Future<String?> _getExistingSignature(int taskId, int formId) async {
    try {
      final realm = RealmDatabase.instance.realm;
      final taskModel =
          realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;

      if (taskModel == null) return null;

      // Find signature folder for this form
      final signatureFolder = taskModel.signatureFolder
          .where((folder) => folder.folderId == formId)
          .firstOrNull;

      if (signatureFolder == null || signatureFolder.signatures.isEmpty) {
        return null;
      }

      // Return the most recent signature URL
      final signature = signatureFolder.signatures.last;
      return signature.signatureUrl;
    } catch (e) {
      logger('Error getting existing signature: $e');
      return null;
    }
  }

  /// Save signature image to app storage
  Future<String?> _saveSignatureImage(Uint8List signatureBytes) async {
    try {
      // Create a temporary file from the bytes
      final tempDir = await getTemporaryDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final tempFile =
          File(path.join(tempDir.path, 'signature_$timestamp.png'));
      await tempFile.writeAsBytes(signatureBytes);

      // Use ImageStorageUtils to save to app storage
      final savedPath = await ImageStorageUtils.saveImageToAppStorage(tempFile);

      // Clean up temporary file
      if (await tempFile.exists()) {
        await tempFile.delete();
      }

      return savedPath;
    } catch (e) {
      logger('Error saving signature image: $e');
      return null;
    }
  }

  /// Save signature to database
  Future<void> _saveSignatureToDatabase({
    required int taskId,
    required int formId,
    required String signatureUrl,
    required String signedBy,
  }) async {
    try {
      final realm = RealmDatabase.instance.realm;

      await realm.writeAsync(() {
        final taskModel =
            realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;

        if (taskModel == null) return;

        // Find or create signature folder for this form
        var signatureFolder = taskModel.signatureFolder
            .where((folder) => folder.folderId == formId)
            .firstOrNull;

        if (signatureFolder == null) {
          signatureFolder = SignatureFolderModel(
            folderId: formId,
            folderName: 'Form $formId Signatures',
            modifiedTimeStampSignaturetype: DateTime.now(),
          );
          taskModel.signatureFolder.add(signatureFolder);
        }

        // Create new signature
        final signature = SignatureModel(
          signatureId: DateTime.now().millisecondsSinceEpoch,
          formId: formId,
          signatureUrl: signatureUrl,
          signedBy: signedBy,
          modifiedTimeStampSignature: DateTime.now(),
          userDeletedSignature: false,
          cannotUploadMandatory: false,
        );

        signatureFolder.signatures.add(signature);
        signatureFolder.modifiedTimeStampSignaturetype = DateTime.now();
        taskModel.modifiedTimeStampSignatures = DateTime.now();
      });

      logger('Signature saved to database successfully');
    } catch (e) {
      logger('Error saving signature to database: $e');
      rethrow;
    }
  }
}
