import 'package:flutter/foundation.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_calendar_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_tasks_usecase.dart';

class SyncService {
  SyncService._internal();

  static final SyncService _instance = SyncService._internal();

  factory SyncService() => _instance;

  final ValueNotifier<bool> isSyncing = ValueNotifier(false);

  Future<void> sync() async {
    if (isSyncing.value) return;

    try {
      isSyncing.value = true;
      // Run sync operations in parallel
      await Future.wait([
        _syncTasks(),
        _syncCalendar(),
      ]);
    } finally {
      isSyncing.value = false;
    }
  }

  Future<void> _syncTasks() async {
    try {
      final token = await sl<DataManager>().getAuthToken() ?? '';
      final id = await sl<DataManager>().getUserId() ?? '';
      const String actualDeviceUid = "8b7a6774c878a206";
      const String actualAppVersion = "9.9.9";

      final request = TasksRequestEntity(
        deviceUid: actualDeviceUid,
        userId: id,
        appversion: actualAppVersion,
        tasks: const [],
        token: token,
      );

      await sl<GetTasksUseCase>().call(request, isSync: true);

      // The repository now handles caching, so no need to save here.
      // if (result.isSuccess && result.data != null) {
      //   await sl<HomeLocalDataSource>().saveTasks(result.data!);
      // }
    } catch (e) {
      // Handle or log error appropriately
    }
  }

  Future<void> _syncCalendar() async {
    try {
      final token = await sl<DataManager>().getAuthToken() ?? '';
      final id = await sl<DataManager>().getUserId() ?? '';

      final params = GetCalendarParams(token: token, userId: id);
      await sl<GetCalendarUseCase>().call(params, isSync: true);
    } catch (e) {
      // Handle or log error appropriately
    }
  }
}
