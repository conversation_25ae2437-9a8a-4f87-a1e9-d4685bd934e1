class SyncSignInfoRequestEntity {
    int? userId;
    String? token;
    List<Task>? tasks;
    String? deviceuid;

    SyncSignInfoRequestEntity({
        this.userId,
        this.token,
        this.tasks,
        this.deviceuid,
    });

}

class Task {
    int? taskId;
    bool? uploadSignatureSuccess;
    DateTime? modifiedTimeStampSignatures;
    List<SignatureFolder>? signatureFolder;

    Task({
        this.taskId,
        this.uploadSignatureSuccess,
        this.modifiedTimeStampSignatures,
        this.signatureFolder,
    });

}

class SignatureFolder {
    int? folderId;
    String? deleteSignaturesIds;

    SignatureFolder({
        this.folderId,
        this.deleteSignaturesIds,
    });

}
