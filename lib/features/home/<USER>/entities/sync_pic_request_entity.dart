class SyncPicInfoRequestEntity {
    int? userId;
    String? token;
    List<Task>? tasks;
    String? deviceuid;

    SyncPicInfoRequestEntity({
        this.userId,
        this.token,
        this.tasks,
        this.deviceuid,
    });

}

class Task {
    int? taskId;
    DateTime? modifiedTimeStampPhotos;
    bool? uploadPhotosSuccess;
    List<PhotoFolder>? photoFolder;

    Task({
        this.taskId,
        this.modifiedTimeStampPhotos,
        this.uploadPhotosSuccess,
        this.photoFolder,
    });

}

class PhotoFolder {
    int? folderId;
    String? deletePhotosIds;

    PhotoFolder({
        this.folderId,
        this.deletePhotosIds,
    });

}